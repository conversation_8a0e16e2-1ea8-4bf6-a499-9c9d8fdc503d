import { randomUUID } from 'crypto';

/**
 * Gera um UUID único para usar em testes
 */
export function generateTestUUID(): string {
  return randomUUID();
}

/**
 * Gera um external_id único para testes
 */
export function generateTestExternalId(): string {
  return `test-${randomUUID()}`;
}

/**
 * Cria dados de teste para eventos outbox
 */
export function createTestOutboxEventData(overrides: any = {}) {
  return {
    event_type: 'payment_created',
    aggregate_id: 1,
    external_id: generateTestExternalId(),
    payload: '{}',
    status: 'pending',
    retry_count: 0,
    ...overrides
  };
}
