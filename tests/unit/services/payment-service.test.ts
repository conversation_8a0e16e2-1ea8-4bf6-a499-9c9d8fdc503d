import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PaymentService } from '../../../src/services/payment-service';
import { PagamentoRepository } from '../../../src/database/repositories/pagamento-repository';
import { OutboxService } from '../../../src/services/outbox-service';
import { DatabaseConnection } from '../../../src/database/connection/database-connection';
import paymentsFixture from '../../fixtures/payments.json';

describe('PaymentService', () => {
  let paymentService: PaymentService;
  let testDb: DatabaseConnection;

  beforeEach(() => {
    // Usar banco de teste configurado no setup
    testDb = (global as any).testDb;
    paymentService = new PaymentService();
  });

  describe('createPaymentWithOutbox', () => {
    it('deve criar pagamento e evento outbox em transação atômica', async () => {
      // Arrange
      const paymentData = paymentsFixture.validPayment;

      // Act
      const result = await paymentService.createPaymentWithOutbox(paymentData);

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBeGreaterThan(0);
      expect(result.valor).toBe(paymentData.valor);
      expect(result.descricao).toBe(paymentData.descricao);
      expect(result.status).toBe('pendente');
      expect(result['id-externo']).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);

      // Verificar se evento outbox foi criado
      const outboxService = new OutboxService();
      const events = outboxService.getPendingEvents(10);
      
      expect(events).toHaveLength(1);
      expect(events[0].event_type).toBe('payment_created');
      expect(events[0].aggregate_id).toBe(result.id);
      expect(events[0].external_id).toBe(result['id-externo']);
      expect(events[0].status).toBe('pending');
      expect(events[0].retry_count).toBe(0);
      
      // Verificar payload do evento
      const payload = JSON.parse(events[0].payload);
      expect(payload.id).toBe(result.id);
      expect(payload.valor).toBe(result.valor);
      expect(payload['id-externo']).toBe(result['id-externo']);
    });

    it('deve fazer rollback se criação do evento outbox falhar', async () => {
      // Arrange
      const paymentData = paymentsFixture.validPayment;
      
      // Mock OutboxService para falhar
      const originalCreate = OutboxService.prototype.createPaymentEvent;
      vi.spyOn(OutboxService.prototype, 'createPaymentEvent')
        .mockImplementation(() => {
          throw new Error('Falha simulada na criação do evento outbox');
        });

      // Act & Assert
      await expect(paymentService.createPaymentWithOutbox(paymentData))
        .rejects.toThrow('Falha simulada na criação do evento outbox');

      // Verificar que nenhum pagamento foi criado
      const paymentRepository = new PagamentoRepository();
      const payments = paymentRepository.findAll();
      expect(payments).toHaveLength(0);

      // Verificar que nenhum evento outbox foi criado
      const outboxService = new OutboxService();
      const events = outboxService.getPendingEvents(10);
      expect(events).toHaveLength(0);

      // Restaurar mock
      vi.restoreAllMocks();
    });

    it('deve fazer rollback se criação do pagamento falhar', async () => {
      // Arrange
      const paymentData = paymentsFixture.invalidPayment; // valor negativo

      // Act & Assert
      await expect(paymentService.createPaymentWithOutbox(paymentData))
        .rejects.toThrow();

      // Verificar que nenhum evento outbox foi criado
      const outboxService = new OutboxService();
      const events = outboxService.getPendingEvents(10);
      expect(events).toHaveLength(0);
    });

    it('deve gerar UUID único para cada pagamento', async () => {
      // Arrange
      const paymentData = paymentsFixture.validPayment;

      // Act
      const payment1 = await paymentService.createPaymentWithOutbox(paymentData);
      const payment2 = await paymentService.createPaymentWithOutbox(paymentData);

      // Assert
      expect(payment1['id-externo']).not.toBe(payment2['id-externo']);
      expect(payment1.id).not.toBe(payment2.id);
    });

    it('deve criar evento outbox com payload correto', async () => {
      // Arrange
      const paymentData = paymentsFixture.highValuePayment;

      // Act
      const payment = await paymentService.createPaymentWithOutbox(paymentData);

      // Assert
      const outboxService = new OutboxService();
      const events = outboxService.getPendingEvents(10);
      const event = events[0];
      const payload = JSON.parse(event.payload);

      expect(payload).toEqual({
        id: payment.id,
        valor: payment.valor,
        descricao: payment.descricao,
        status: payment.status,
        created_at: payment.created_at,
        updated_at: payment.updated_at,
        'id-externo': payment['id-externo']
      });
    });
  });
});
