import { describe, it, expect, beforeEach, vi } from 'vitest';
import { BackgroundWorker } from '../../../src/services/background-worker';
import { OutboxService } from '../../../src/services/outbox-service';
import { OutboxEvent } from '../../../src/database/types/outbox';
import { PaymentExternalService } from '../../../src/services/payment-external-service';

describe('BackgroundWorker', () => {
  let backgroundWorker: BackgroundWorker;
  let outboxService: OutboxService;

  beforeEach(() => {
    outboxService = new OutboxService();
    backgroundWorker = new BackgroundWorker(outboxService);
  });

  describe('shouldRetry', () => {
    it('deve permitir retry quando delay passou e retry_count < 3', () => {
      // Arrange
      const now = Date.now();
      const fiveSecondsAgo = new Date(now - 5000).toISOString();
      
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: fiveSecondsAgo,
        retry_count: 1,
        last_retry_at: fiveSecondsAgo
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(true);
    });

    it('deve bloquear retry quando atingiu máximo de tentativas', () => {
      // Arrange
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: new Date().toISOString(),
        retry_count: 3
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(false);
    });

    it('deve bloquear retry quando delay não passou', () => {
      // Arrange
      const now = Date.now();
      const twoSecondsAgo = new Date(now - 2000).toISOString();
      
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: twoSecondsAgo,
        retry_count: 1,
        last_retry_at: twoSecondsAgo
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(false);
    });

    it('deve usar created_at quando last_retry_at não existe', () => {
      // Arrange
      const now = Date.now();
      const fiveSecondsAgo = new Date(now - 5000).toISOString();
      
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: fiveSecondsAgo,
        retry_count: 1
        // last_retry_at não definido
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(true);
    });
  });

  describe('getRetryDelay', () => {
    it('deve calcular delay correto para retry_count 0', () => {
      // Act
      const delay = (backgroundWorker as any).getRetryDelay(0);

      // Assert
      expect(delay).toBe(2000); // 2 segundos
    });

    it('deve calcular delay correto para retry_count 1', () => {
      // Act
      const delay = (backgroundWorker as any).getRetryDelay(1);

      // Assert
      expect(delay).toBe(4000); // 4 segundos
    });

    it('deve calcular delay correto para retry_count 2', () => {
      // Act
      const delay = (backgroundWorker as any).getRetryDelay(2);

      // Assert
      expect(delay).toBe(8000); // 8 segundos
    });

    it('deve implementar backoff exponencial', () => {
      // Act & Assert
      const delay0 = (backgroundWorker as any).getRetryDelay(0);
      const delay1 = (backgroundWorker as any).getRetryDelay(1);
      const delay2 = (backgroundWorker as any).getRetryDelay(2);

      expect(delay1).toBe(delay0 * 2);
      expect(delay2).toBe(delay1 * 2);
    });
  });

  describe('processEvent', () => {
    it('deve processar evento com sucesso', async () => {
      // Arrange
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: JSON.stringify({
          id: 1,
          valor: 100.50,
          'id-externo': 'test-uuid'
        }),
        status: 'pending',
        created_at: new Date().toISOString(),
        retry_count: 0
      };

      // Mock PaymentExternalService para sucesso
      const mockSendPayment = vi.fn().mockResolvedValue({
        status: 201,
        statusText: 'Created'
      });

      // Substituir método do PaymentExternalService
      vi.spyOn(PaymentExternalService.prototype, 'sendPayment').mockImplementation(mockSendPayment);

      // Act
      await (backgroundWorker as any).processEvent(event);

      // Assert
      expect(mockSendPayment).toHaveBeenCalledWith({
        id: 1,
        valor: 100.50,
        'id-externo': 'test-uuid'
      });

      // Verificar se evento foi marcado como processado
      const processedEvent = outboxService.findByExternalId('test-uuid');
      expect(processedEvent?.status).toBe('processed');

      vi.restoreAllMocks();
    });

    it('deve marcar evento como failed quando API externa falha', async () => {
      // Arrange
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: JSON.stringify({
          id: 1,
          valor: 100.50,
          'id-externo': 'test-uuid'
        }),
        status: 'pending',
        created_at: new Date().toISOString(),
        retry_count: 0
      };

      // Mock PaymentExternalService para falhar
      const mockSendPayment = vi.fn().mockRejectedValue(
        new Error('API returned status 429: Too Many Requests')
      );

      vi.spyOn(PaymentExternalService.prototype, 'sendPayment').mockImplementation(mockSendPayment);

      // Act
      await (backgroundWorker as any).processEvent(event);

      // Assert
      const failedEvent = outboxService.findByExternalId('test-uuid');
      expect(failedEvent?.status).toBe('failed');
      expect(failedEvent?.retry_count).toBe(1);
      expect(failedEvent?.error_message).toContain('API returned status 429');

      vi.restoreAllMocks();
    });
  });
});
