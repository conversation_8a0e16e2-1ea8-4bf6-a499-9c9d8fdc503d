import { describe, it, expect, beforeEach } from 'vitest';
import { BackgroundWorker } from '../../../src/services/background-worker';
import { OutboxService } from '../../../src/services/outbox-service';
import { OutboxEvent } from '../../../src/database/types/outbox';

describe('BackgroundWorker - Testes Básicos', () => {
  let backgroundWorker: BackgroundWorker;
  let outboxService: OutboxService;

  beforeEach(() => {
    outboxService = new OutboxService();
    backgroundWorker = new BackgroundWorker(outboxService);
  });

  describe('shouldRetry', () => {
    it('deve permitir retry quando delay passou e retry_count < 3', () => {
      // Arrange
      const now = Date.now();
      const fiveSecondsAgo = new Date(now - 5000).toISOString();
      
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: fiveSecondsAgo,
        retry_count: 1,
        last_retry_at: fiveSecondsAgo
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(true);
    });

    it('deve bloquear retry quando atingiu máximo de tentativas', () => {
      // Arrange
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: new Date().toISOString(),
        retry_count: 3
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(false);
    });

    it('deve bloquear retry quando delay não passou', () => {
      // Arrange
      const now = Date.now();
      const twoSecondsAgo = new Date(now - 2000).toISOString();
      
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: twoSecondsAgo,
        retry_count: 1,
        last_retry_at: twoSecondsAgo
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(false);
    });

    it('deve usar created_at quando last_retry_at não existe', () => {
      // Arrange
      const now = Date.now();
      const fiveSecondsAgo = new Date(now - 5000).toISOString();
      
      const event: OutboxEvent = {
        id: 1,
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        created_at: fiveSecondsAgo,
        retry_count: 1
        // last_retry_at não definido
      };

      // Act
      const shouldRetry = (backgroundWorker as any).shouldRetry(event);

      // Assert
      expect(shouldRetry).toBe(true);
    });
  });

  describe('getRetryDelay', () => {
    it('deve calcular delay correto para retry_count 0', () => {
      // Act
      const delay = (backgroundWorker as any).getRetryDelay(0);

      // Assert
      expect(delay).toBe(2000); // 2 segundos
    });

    it('deve calcular delay correto para retry_count 1', () => {
      // Act
      const delay = (backgroundWorker as any).getRetryDelay(1);

      // Assert
      expect(delay).toBe(4000); // 4 segundos
    });

    it('deve calcular delay correto para retry_count 2', () => {
      // Act
      const delay = (backgroundWorker as any).getRetryDelay(2);

      // Assert
      expect(delay).toBe(8000); // 8 segundos
    });

    it('deve implementar backoff exponencial', () => {
      // Act & Assert
      const delay0 = (backgroundWorker as any).getRetryDelay(0);
      const delay1 = (backgroundWorker as any).getRetryDelay(1);
      const delay2 = (backgroundWorker as any).getRetryDelay(2);

      expect(delay1).toBe(delay0 * 2);
      expect(delay2).toBe(delay1 * 2);
    });
  });

  describe('Validação de instância', () => {
    it('deve criar instância do BackgroundWorker corretamente', () => {
      // Assert
      expect(backgroundWorker).toBeDefined();
      expect(backgroundWorker).toBeInstanceOf(BackgroundWorker);
    });

    it('deve ter OutboxService configurado', () => {
      // Assert
      expect(outboxService).toBeDefined();
      expect(outboxService).toBeInstanceOf(OutboxService);
    });
  });
});
